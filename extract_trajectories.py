import json
import re
import os

def extract_action_from_value(value):
    """从GPT回复的value中提取Action后面的内容"""
    # 使用正则表达式匹配Action:后面的内容
    action_match = re.search(r'Action:\s*(.+?)(?:\n|$)', value, re.IGNORECASE)
    if action_match:
        return action_match.group(1).strip()
    return None

def extract_trajectories(input_file, max_files=20):
    """提取专家轨迹并生成JSON文件"""
    
    # 读取原始JSON文件
    with open(input_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # 创建输出目录
    output_dir = "trajectories"
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 处理前max_files个对话
    for i, conversation_data in enumerate(data[:max_files]):
        item_id = conversation_data.get('item_id', f'unknown_{i}')
        conversations = conversation_data.get('conversations', [])
        
        # 提取教师轨迹（专家动作）
        teacher_trajectory = []
        
        for conv in conversations:
            if (conv.get('from') == 'gpt' and 
                conv.get('loss') == True and 
                conv.get('value')):
                
                action = extract_action_from_value(conv['value'])
                if action:
                    teacher_trajectory.append(action)
        
        # 创建轨迹数据
        trajectory_data = {
            "teacher_trajectory": teacher_trajectory,
            "student_trajectory": []
        }
        
        # 生成文件名并保存
        filename = f"{item_id}.json"
        filepath = os.path.join(output_dir, filename)
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(trajectory_data, f, ensure_ascii=False, indent=2)
        
        print(f"已生成文件 {i+1}/{max_files}: {filename}")
        print(f"  教师轨迹包含 {len(teacher_trajectory)} 个动作")
        if teacher_trajectory:
            print(f"  示例动作: {teacher_trajectory[0]}")
        print()

if __name__ == "__main__":
    # 提取前20个文件的轨迹
    extract_trajectories("alfworld_train.json", max_files=20)
    print("轨迹提取完成！")
